{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "WorkHelper", "version": "0.0.1", "identifier": "com.msglbo.workhelper", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:5173", "beforeDevCommand": "pnpm dev", "beforeBuildCommand": "pnpm build"}, "app": {"windows": [{"label": "main", "title": "工作助手", "width": 1000, "height": 700, "resizable": false, "fullscreen": false, "decorations": false, "center": true, "visible": false, "transparent": true}], "security": {"csp": "default-src 'self'; connect-src 'self' https://ai.mufengweilai.com https://api.deepseek.com; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:", "dangerousDisableAssetCspModification": false}, "withGlobalTauri": false}, "plugins": {}, "bundle": {"active": true, "targets": ["nsis"], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "publisher": "Msg-Lbo", "copyright": "Copyright © 2024 Msg-Lbo. All rights reserved.", "category": "Productivity", "shortDescription": "工作助手 - 智能日报周报生成工具", "longDescription": "工作助手是一款智能化的日报周报生成工具，通过分析工作内容自动生成规范的工作汇报，并支持一键提交到OA系统，大幅提升工作效率。", "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": "", "nsis": {"installMode": "perMachine", "languages": ["SimpChinese"], "displayLanguageSelector": false, "installerIcon": "icons/icon.ico"}}}}