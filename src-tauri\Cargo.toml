[package]
name = ""
version = "0.0.1"
description = "A Tauri App"
authors = ["you"]
license = ""
repository = ""
edition = "2021"
rust-version = "1.77.2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.3.1", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
log = "0.4"
tauri = { version = "2.7.0", features = [] }
tauri-plugin-log = "2"
tauri-plugin-dialog = "2"
tauri-plugin-shell = "2"
tokio = { version = "1.0", features = ["full"] }
encoding_rs = "0.8"
reqwest = { version = "0.11", features = ["json", "stream"] }
futures-util = "0.3"
base64 = "0.21"
dirs = "5.0"
dotenv = "0.15"
