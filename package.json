{"name": "githelper-tauri", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "start": "tauri dev", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "release:patch": "node scripts/version-manager.cjs patch", "release:minor": "node scripts/version-manager.cjs minor", "release:major": "node scripts/version-manager.cjs major", "release:rollback": "node scripts/version-manager.cjs rollback", "test:env": "node scripts/version-manager.cjs --test-env"}, "dependencies": {"@tauri-apps/api": "^2.7.0", "@tauri-apps/plugin-dialog": "^2.3.1", "@tauri-apps/plugin-shell": "^2.3.0", "@vicons/ionicons5": "^0.13.0", "naive-ui": "^2.42.0", "vue": "^3.5.17"}, "devDependencies": {"@tauri-apps/cli": "^2.7.1", "@types/node": "^24.1.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "dotenv": "^16.4.5", "sass-embedded": "^1.89.2", "semver": "^7.6.3", "simple-git": "^3.27.0", "typescript": "~5.8.3", "vite": "^7.0.4", "vue-tsc": "3.0.3"}}