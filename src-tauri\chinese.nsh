; Git Helper 中文语言文件
; 简体中文 (Simplified Chinese)

!insertmacro LANGFILE "SimpChinese" "简体中文"

; 安装程序标题
LangString INSTALLER_TITLE ${LANG_SIMPCHINESE} "Git Helper 安装向导"
LangString INSTALLER_SUBTITLE ${LANG_SIMPCHINESE} "这将在您的计算机上安装 Git Helper。"

; 欢迎页面
LangString WELCOME_TITLE ${LANG_SIMPCHINESE} "欢迎使用 Git Helper 安装向导"
LangString WELCOME_TEXT ${LANG_SIMPCHINESE} "这个向导将指导您完成 Git Helper 的安装过程。$\r$\n$\r$\n建议您在开始安装前关闭所有其他应用程序。这将使安装程序能够更新相关的系统文件，而无需重新启动您的计算机。$\r$\n$\r$\n点击 下一步 继续。"

; 许可协议页面
LangString LICENSE_TITLE ${LANG_SIMPCHINESE} "许可协议"
LangString LICENSE_SUBTITLE ${LANG_SIMPCHINESE} "请仔细阅读以下许可协议。"
LangString LICENSE_TEXT ${LANG_SIMPCHINESE} "如果您接受协议中的条款，请选择 我接受协议 然后点击 下一步。您必须接受协议才能安装 Git Helper。"

; 安装目录页面
LangString DIRECTORY_TITLE ${LANG_SIMPCHINESE} "选择安装位置"
LangString DIRECTORY_SUBTITLE ${LANG_SIMPCHINESE} "选择要安装 Git Helper 的文件夹。"
LangString DIRECTORY_TEXT ${LANG_SIMPCHINESE} "安装程序将把 Git Helper 安装到以下文件夹。要安装到不同的文件夹，请点击 浏览 并选择其他文件夹。点击 下一步 继续。"

; 开始菜单页面
LangString STARTMENU_TITLE ${LANG_SIMPCHINESE} "选择开始菜单文件夹"
LangString STARTMENU_SUBTITLE ${LANG_SIMPCHINESE} "选择用于程序快捷方式的开始菜单文件夹。"
LangString STARTMENU_TEXT ${LANG_SIMPCHINESE} "安装程序将在以下开始菜单文件夹中创建程序的快捷方式。"

; 安装页面
LangString INSTALLING_TITLE ${LANG_SIMPCHINESE} "正在安装"
LangString INSTALLING_SUBTITLE ${LANG_SIMPCHINESE} "请等待 Git Helper 安装完成。"

; 完成页面
LangString FINISH_TITLE ${LANG_SIMPCHINESE} "安装完成"
LangString FINISH_SUBTITLE ${LANG_SIMPCHINESE} "Git Helper 已成功安装到您的计算机上。"
LangString FINISH_TEXT ${LANG_SIMPCHINESE} "Git Helper 已经安装完成。点击 完成 退出安装向导。"
LangString FINISH_RUN ${LANG_SIMPCHINESE} "运行 Git Helper"

; 卸载程序
LangString UNINSTALL_TITLE ${LANG_SIMPCHINESE} "卸载 Git Helper"
LangString UNINSTALL_TEXT ${LANG_SIMPCHINESE} "这将从您的计算机中删除 Git Helper。"

; 按钮文本
LangString BUTTON_NEXT ${LANG_SIMPCHINESE} "下一步(&N) >"
LangString BUTTON_BACK ${LANG_SIMPCHINESE} "< 上一步(&B)"
LangString BUTTON_INSTALL ${LANG_SIMPCHINESE} "安装(&I)"
LangString BUTTON_CANCEL ${LANG_SIMPCHINESE} "取消"
LangString BUTTON_CLOSE ${LANG_SIMPCHINESE} "关闭(&C)"
LangString BUTTON_BROWSE ${LANG_SIMPCHINESE} "浏览(&B)..."
LangString BUTTON_FINISH ${LANG_SIMPCHINESE} "完成(&F)"

; 错误消息
LangString ERROR_ADMIN_REQUIRED ${LANG_SIMPCHINESE} "您需要管理员权限来安装此程序。"
LangString ERROR_ALREADY_RUNNING ${LANG_SIMPCHINESE} "Git Helper 正在运行。请先关闭程序再继续安装。"
LangString ERROR_DISK_SPACE ${LANG_SIMPCHINESE} "磁盘空间不足。请释放一些空间后重试。"

; 其他文本
LangString APP_NAME ${LANG_SIMPCHINESE} "Git Helper"
LangString APP_DESCRIPTION ${LANG_SIMPCHINESE} "专业的 Git 版本控制管理工具"
